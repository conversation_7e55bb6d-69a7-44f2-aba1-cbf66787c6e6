# 如何获取用户并实现增长 - Alex Schultz

**演讲者**: <PERSON>，Facebook增长副总裁
**时长**: 49分21秒
**演讲系列**: CS183F

---

## 演讲介绍

大家下午好。今天我们请到了Alex Schultz，Facebook的增长副总裁，可能是世界上增长领域的专家。正如他在这个周末可以证明的那样，当我遇到真正困难的增长问题时，我会去找他。许多在Facebook工作的人，实际上许多Facebook的创始人和早期员工都说，雇用Alex是Facebook一个不为人知的成功秘诀。你大约十年前加入Facebook，我想那时Facebook的用户还不到1亿。

**Alex Schultz**: 是的。

**主持人**: 远少于1亿用户。Facebook现在有16亿用户，而Alex在拥有优秀产品的重要性之后，可能是推动这一增长的第二重要因素。

**Alex Schultz**: Havi、Naomi，那些非常重要的人。

**主持人**: 还有团队。无论如何，非常感谢你来为我们演讲。

---

## 演讲正文

**Alex Schultz**: 谢谢。我没有为那个介绍付钱给Sam，我要指出Havi是我的老板，所以嗨Havi。但是，Havi和Naomi、Danny，这些人在增长团队工作了四年，还有像Blake、Ray和James Wang这样建立团队的人，这很好，但我是增长方面的一个VP，我不是增长的VP。

你们有一个令人惊叹的讲座系列。我昨天刚看完所有讲座，以确保我不会在没有实际看过其他人说什么的情况下说任何话，这完全太棒了。我喜欢D'Angelo所说的，所以如果你没有在这里，如果你没有看那个讲座，我强烈建议你回去看看Adam D'Angelo说了什么，因为我无法过分强调他在指标方面的观点。

所以我要在前半部分尝试回顾一些已经提出的观点，并强调我认为在考虑增长时最重要的是什么，以及我的观点和我们Facebook增长团队的观点，然后在后半部分尝试与你们谈论一些特别有用的策略。

我认为你们应该接受的警告是Stuart从Slack和Flickr所说的，在某种程度上你确实有1亿只猴子在打字机上敲击。我很幸运能在Facebook的增长团队工作，能够在像Facebook这样具有Facebook那样留存率的产品上工作，世界上每个人都想使用这个产品。说我推动了它，或者我们中的任何人推动了它，超出了有很好的产品市场匹配，也许不是真的。我无法告诉你如果我们在Facebook没有增长团队会是什么样的AB测试。

## 增长最重要的因素：留存

**对增长来说最重要的是什么？增长最重要的单一因素是留存。**

Adam在他的演讲中评论了这一点，他向你们展示了一些真实公司的队列。但这里是你应该如何看待留存的风格化队列。

获取后的天数在x轴上，y轴是仍然月活跃的人的百分比。我非常关心使用月活跃，而不是日活跃，不是周活跃。人们总是说使用日活跃有很好的论据，因为它关于使用强度，但真正重要的是你有利害关系，人们至少每月回到你的产品一次，然后你在此基础上建立参与度、收入，无论你的关键指标是什么，我们稍后会讲到。但首先我相信月活跃是最重要的事情。

第二件事是你如何真正计算它？所以我有一个来自Facebook广告产品前250天的真实收入图表，我稍后会向你展示。但我认为真正重要的是说，即使你只有一千或一百个用户，计算这条曲线也很容易。

你所做的是查看注册后第一天的所有用户。他们是月活跃的吗？是的，显然。前三十天，每个人都将是月活跃的。顺便说一下，我更喜欢你将一个月定义为28天，因为7的倍数对于这种指标真的很好，因为周的原因。但在前30天，每个人都在他们的第一天、第二天、第三天作为月活跃用户活跃，无论他们是否回来。

然后你做的是查看第31天。显然只有一些用户会活跃31天。一天前注册的每个人都没有活跃31天。那么他们中有多少百分比在第31天活跃？他们中有多少百分比在第32天活跃？随着你在注册后天数上走得越来越远，在你的服务上那么长时间的人数下降。这意味着你的指标会变得更嘈杂，对吧？当你到达曲线的右侧时。但这并不意味着你不能在用户数量很少的情况下很早就掌握事情趋于平缓的地方。

当我谈论留存作为指标时，人们给我的第一个批评，甚至当你看Adam在他之前的演示中提出的内容时，是你需要大量用户，所以你需要数百万用户来计算。这不是真的。例如，我们在Facebook广告商的早期就用数千用户做到了这一点。或者，你需要让所有这些用户使用很长时间。看看Adam的图表，那是多年的月份和月份。不，你可以一天一天地做，使用这种方法你可以非常快地到达一个非常好的地方。

## 留存曲线的类型

**那么你在留存曲线中寻找什么？你寻找它在零以上平线，不是像心脏监护仪那样的平线。你希望它在与x轴渐近的某条线上平线。**

我见过三种类型的这些曲线：

1. **第一种类型**是它渐近于x轴，真的非常接近，下降到几乎为零。你听说过很多这样的公司，你想想大多数游戏，对吧？大多数游戏都是轰动一时的热门。现在有多少人使用Farmville与在其绝对巅峰时使用Farmville的人数相比？请告诉我你知道Farmville是什么。是的，好的。现在有多少人使用Farmville与过去使用它的人数相比？Farmville是一个轰动一时的热门。

2. **然后还有其他看起来像这样的，它们稳定下来。**所以你想想Minecraft，对吧？或者魔兽世界。这些游戏以某种方式具有网络效应，人们长期留在其中。

3. **我见过的这条曲线的另一个版本**是那些下降的，看起来它们要碰到x轴，然后重新加速并上升。有几种不同的事情可以导致这种情况，对吧？所以你有这种压倒性的东西驱使你下降到零留存，然后数字再次上升。

**驱动业务上升的因素：**

**网络效应**: 当你所有的朋友都使用Facebook时，你更有可能使用Facebook。当你所有的朋友都使用Twitter时，你更有可能使用Twitter、WhatsApp、Messenger，无论你谈论的是哪种社交服务。有真正重要的网络效应。网络效应在市场中也很重要。当你有足够的卖家时，你可以有足够的买家，当你有足够的流量时，你会得到更多的卖家。所以随着市场的发展，你经常发现这些注册的老队列复活并回到服务中。

**开放新界面**: 很多人首先在iOS上发布他们的应用。实际上我喜欢Jan上周的演讲，他提出从诺基亚开始，专注于Android，因为这是世界上大多数人使用的。但在硅谷，我们通常首先在iOS上构建我们的应用。然后当有人发布Android应用程序时，这是我多年前在Instagram上看到的。但当你发布Android应用程序时，人们可能在iPhone上尝试过它，然后将他们的iPhone换成Android，不再能够访问你的服务。人们可能在朋友的iPhone上尝试过你的服务，然后当你添加Android时，他们回来使用你的服务。

**添加类别**: 想想亚马逊。有一个很好的讨论，那是Stuart之前还是...之前有人谈论亚马逊以及亚马逊如何从书籍开始。因为有大量可用的书籍库存。但然后他们添加了另一个类别，然后随着时间的推移添加了另一个类别。想象一下，会有人来亚马逊买那本书，但两年后亚马逊突然有了厨具，我不知道，或者时尚。他们回来然后买时尚，这给了他们更多的用例来回来更多的月活跃。

这对eBay来说是真的，我们添加了汽车。我记得在eBay时，我们必须做所有关于商品总交易量的报告，有和没有汽车，没有人相信你可以在线购买汽车。eBay做到了，它为人们创造了一个全新的用例，其中许多人是现有的eBay客户，现在有了一个更多的类别可以购物。

**所以，你想要一条渐近于与x轴平行的线的曲线。你可以通过使用我之前概述的技术，用比大多数人认为可能的更有限的数据量来做到这一点，我将在几张幻灯片中向你展示一个真实世界的例子。我见过三种类型的曲线。像这样渐近的曲线，太棒了。你的公司将生存。它会变得多大，我们有另一个问题。直接归零的曲线，那没关系。你可能在游戏中，你的工作是一个接一个地制作热门。EA不期望无论什么，FIFA 2012如果他们发布了FIFA 27现在仍然有很多用户。所以没关系，有些业务碰到x轴是可以的，但你需要知道你在其中一个业务中。然后我见过的第三个是曲线上升，这可以由网络效应驱动，可以由在更多平台上可用驱动，可以由向你的市场或服务添加类别驱动。**

## 增长会计：一种看待增长的绝佳方式

从这里产生了一种看待增长的绝佳方式，这个叫Danny Ferante的人，他在雅虎工作，是增长团队的第一批成员之一，现在负责Facebook的核心数据科学，他完全很棒，他真的是操作背后的大脑和嘴巴。Danny把这个带到了Facebook，这是一种看待增长的简单方式。

我们将增长看作我们每周增长多少用户。我们以绝对基础来看待它。这样看，我们就像，"嗯，所以我们每周注册无论多少，一百万，我们每周增长无论多少，80万，我不知道。"酷，我们流失了20万人。我们从来没有真正以这种方式看待它。

当我们改变看待事物的方式，说，"好的，净增长是每周80万。新用户是每周一百万。但流失和复活是什么？"我们当时实际发现的，我记不住绝对数字，所以我就编一些，但想象你流失130万。所以不是20万，是130万。你复活110万。所以，加100万，减130万，加110万，你得到净80万的增长，但那80万的增长是由流失和复活的不同数量级驱动的，比你意识到的。所以这帮助你专注于如何阻止人们流失？如何让现在不回到我的服务的人回到我的服务？

所以一旦你有了你的留存曲线，一旦你开始有一个有留存的业务，你需要看你的增长会计，不仅仅是我们获取了多少用户。有意义吗？

总的来说，我专注于增长的第一件事，我们专注于增长的第一件事，是留存。

## 魔法时刻：驱动留存的关键

**那么什么让你在任何给定的服务中留存？魔法时刻。**

当你使用AirBnB时的那个时刻是什么？当你使用DoorDash、Slack、Facebook、LinkedIn、WhatsApp时的那个时刻是什么？无论什么产品，那个你说"是的。我明白了"的时刻是什么？

在Facebook上是朋友。你看到第一个朋友，那个人，我完全搞错了人群，那个来自高中的人，当你长大并搬走时你失去了联系。但你看到那个人，你说，"哇，我多年没有和他们说话了。让我们看看他们的生活发生了什么。"他们有了孩子，也失去了头发，你对自己感觉更好。这是Facebook上的魔法时刻。

**那么你如何快速驱动用户到那个魔法时刻？**

你看AirBnB，对吧？他们谈论过他们给人们的喜悦，他们谈论过他们创建的用户旅程地图中用户旅程阶段的这些不同截图。让第一个人预订你的房子，住在第一个房产，这些是我们的魔法时刻。

eBay，从陌生人那里在线购买你的第一件物品。现在这似乎不疯狂，但当我在英国eBay工作时，这是革命性的，当你和人们谈论它时，人们真的眼睛发亮。在德国，他们有这个很棒的广告，显示了当你等待拍卖结束并进行第一次购买时的积累，那是如此肾上腺素、内啡肽的时刻，你知道吗？用户喜欢它。现在似乎很久以前，但这些魔法时刻是你需要寻找的。

### 如何找到魔法时刻？

**非可扩展的方式**：它们听起来都相对明显吗？你实际上可以思考，我的产品的体验是什么，我希望人们做什么？你可以问你的第一批用户，"好的，你继续使用我的产品，为什么你继续使用我的产品？什么让你高兴使用我的产品？那第一个时刻是什么？"这是非可扩展的，但通常有效。

当你问人们，"Facebook最好的是什么？"他们说的第一件事是，"与朋友和家人联系。"在所有研究中，"你为什么喜欢Facebook？""哦，它帮助我与朋友和家人联系。"

**可扩展的方式**：寻找相关性。所以，Mark在创业学校自己谈过，他说我们专注于让人们在14天内获得10个朋友。这是一条平滑的曲线，对吧？朋友数量在x轴上，留存百分比在y轴上，不是在10处有阶跃变化，如果你得到9个你不留存，如果你得到11个你留存，一切都在10处改变。不，这显然是一条平滑的曲线，我们在曲线上选择了一个点。但你在前14天拥有的朋友数量与你是否留存之间有很强的相关性。

第一天的朋友数量有相关性，但对任何产品都有相关性。比如，你是否出价，你是否立即购买，或者你是否在eBay上列出物品，这与eBay的留存完全相关。你可以想象，我不知道，我的意思是DoorDash是在斯坦福成立的，对吧？你可以想象对于DoorDash，你下了第一个订单，那个订单到达你的门口。你是否下了订单，你在第一周下了多少订单，可能实际上与你是否留存相关。

**所以你需要通过与用户交谈来寻找你产品的魔法时刻，然后尝试用相关性来验证。有意义吗？**

## 维度推理：确定所需的留存水平

酷。我喜欢这个，我在每次演示中都放它。所以我很幸运在英国剑桥接受了物理学训练，我最喜欢的讲座系列，不是我最喜欢的讲座，而是我最喜欢的讲座系列叫做维度推理。

所以我站在这里说很容易，"好的，所以你需要获得平坦的留存，然后你有一个可行的业务。"这实际上不是真的。这有点是一个轻率的陈述。

如果你正在制作一个社交网络，你有5%的留存，你不会长期拥有一个巨大的社交网络。或者一个消息应用，比如如果只有5%注册WhatsApp的朋友实际使用WhatsApp，那有什么用？我的意思是，它会是无用的，对吧？如果这对我们来说是真的，我们不可能发布超过10亿的活跃用户数量。

另一方面，如果你有一个销售时尚的市场，你有5%或10%的留存，但这些人花很多钱，你有一个完全可行的业务。

**所以你需要的留存水平因你所在的类别而大大不同。显然越高越好在一切中都是真的，比如如果你可以有eBay类型的商业模式或亚马逊类型的商业模式，有90%的留存，你可能最终会比亚马逊更大。我的意思是那将是不可思议的。但我想要说的是，你可以弄清楚。**

我喜欢这个作为一个鼓舞人心的故事。所以Jeffery Taylor是这个英国物理学家，他在曼哈顿计划的边缘，他不在核心。他后来因为不同的领域赢得了诺贝尔奖。美国和俄罗斯政府发布这些图像说，"这是我们的炸弹有多大。"但他们没有发布实际的威力，因为那是一个严密保守的国家机密。

所以Jeffery Taylor所做的是他使用了这种叫做维度推理的技术，你看你试图实现的指标中的单位，所以焦耳，对吧，能量是焦耳。千克乘以米的平方除以秒的平方，这就是焦耳分解成的。在这种情况下，千克，你要使用什么，空气的体积乘以空气的密度，方便地是1，所以你现在有米立方乘以米平方除以秒平方。所以米的五次方乘以秒的负二次方。时间在这里，球体的直径，半径在这里，你只需将半径作为m插入，你将直径，时间作为秒插入，你最终得到25千吨作为输出。你得到10的14次方我认为是它计算出来的。结果炸弹是21千吨，由美国政府测量。他可以拍一张照片并弄清楚当时美国最严密保守的国家机密。

**如果他能弄清楚那个，你可以弄清楚你需要有什么留存。**

## 真实世界的例子：Facebook广告业务

所以这是我承诺的，这是一个实际的曲线，我在几个不同的活动中展示过它，所以我去掉了数字，因为我担心它们是国家机密。但这是从2007年到2008年。我加入的那一周我们开始了我们在Facebook的广告业务，我们的自助广告业务。

粉色对应右手轴，蓝色对应左手轴。蓝色是多少广告商在系统中至少一天、两天、三天，因为x轴是从获取开始的天数，有意义。所以这个东西只存在了250天，所以零人在251天前是广告商，在第251天。所有人在第零天至少活跃过。

然后每个粉色数据点是我们从每个注册的用户那里获得的总收入，他们在第30天或第31天使用服务，除以30或31天前注册的用户数量。不是多少活跃，对吧？多少注册。所以30或31天前注册的队列中的总数，你将他们一天赚的收入除以那个用户数量。这给你那个在那里的曲线形状。

我在2008年和Danny一起做了这个。我希望你从看这条曲线中得到的真正重要的事情是，看粉色线有多平。就像，我一直在谈论曲线趋于平缓，这是一个实际趋于平缓的曲线。看它趋于平缓有多快。到100天时，你基本上可以预测线在200天时会是什么样子。

我一遍又一遍地看到这个，我在参与度方面看到过这个。所以如果你用相同的分母除以花费的时间，我实际上在情感方面看到过这个。如果你有足够大的情感调查，你用相同的分母除以它。我在帖子方面看到过这个。我在上传的照片、发送的消息方面看到过这个。就像，你可以使用我谈论的用于确定你有多少留存以及你的业务是否可行的相同技术，来弄清楚你的一个客户的终身价值是什么。

**如果我今天获取我业务的总可寻址市场，我在100天内会赚多少收入？这就是这条曲线允许你做的。**你可以为帖子预测它，你可以为页面浏览量、花费时间预测它，无论你想要什么。相同的方法适用于参与度，适用于留存。实际上留存通常是这个指标的最大驱动因素。

我们用很少的数据做了这个。后来有一个我们可以发布的统计数据，2012年底我们有大约70万活跃广告商，开始我们有大约30万活跃广告商。这是五年前，所以你可以想象计算这个背后有一个非常小的数据集。

最后一件事是我说事情在你走得越远时变得越嘈杂。你可以在这个图上字面上看到，对吧？你在图上走得越右，数字的方差。
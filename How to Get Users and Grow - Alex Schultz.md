**How_to_Get_Users_and_Grow_-_<PERSON>_<PERSON>_-_CS183F.mp3** (49m 21s)
<https://jotengine.com/transcriptions/gYOFFeB8Mv7WNoD6rjiG1w>
**3 speakers** (Speaker 1, <PERSON>, Speaker 3)

*[0:00:11]*
**Speaker 1**: Good afternoon everybody. Today we have <PERSON>, VP of growth at Facebook, and probably the world expert on growth. As he can vouch from this weekend, the person I go to when I have a really hard growth question. Many people who work at Facebook, many of the founders of Facebook in fact, and very early employees have said hiring <PERSON> has been one of the secret successes of Facebook that is not well understood. You joined in almost ten years ago and I think at that point Facebook had less than 100 million users.

*[0:00:40]*
**<PERSON>**: Yeah.

*[0:00:40]*
**Speaker 1**: Way less than 100 million users. Facebook now has 1.6 billion, and <PERSON> has been, after the importance of having a great product, maybe the second most important driver of that.

*[0:00:50]*
**<PERSON>**: <PERSON><PERSON>, <PERSON>, those very important people.

*[0:00:52]*
**Speaker 1**: And the team. Anyway, thank you very much for coming to speak to us.

*[0:00:55]*
**<PERSON>**: Thank you. I did not pay <PERSON> for that introduction, and I would note that <PERSON><PERSON> is my boss, so hi <PERSON><PERSON>. But no, <PERSON><PERSON> and <PERSON>, <PERSON>, there are these people who have been on the growth team for four years, and then there are people like <PERSON> and and <PERSON> and <PERSON> <PERSON> who set the team up, and it's very nice, but I am a VP on growth, I am not the <PERSON> of growth. So you've had an amazing lecture series. I've just watched all of them yesterday to make sure I didn't say anything without actually having seen what everybody else said, and it was totally awesome. I loved what <PERSON>'<PERSON> had to say, so that lecture if you weren't here, if you didn't watch it, I strongly recommend you go back and see what Adam D'Angelo said, because I could not emphasize his points on metrics more than enough. And so what I'm going to try and do for the first half is go over some of the points that have been made already, and emphasize what I think is most important when you're thinking about growth and what my take is and our take is on the Facebook growth team on those, and then in the second half try and talk to you a little bit more about tactics that have been particularly useful. The caveat that I think you should take on board is what Stuart from Slack and Flickr said, which is to some extent you do have a 100 million monkeys bashing at typewriters. And I am very, very lucky to be on the growth team at Facebook, to get to work on a product like Facebook that has the retention that Facebook does, that has everyone in the world wanting to use the product. And to say that, like, I drove it or any of us drove it beyond there being great product market fit is perhaps not true. I can't tell you what the AB test is if we didn't have a growth team at Facebook. So what matters most to growth? The single most important thing to growth is retention. Adam commented on this in his talk, and he showed you cohorts from some real company. But here is a stylized cohort of how you should look at retention. The number of days from acquisition is on the x axis, on the y axis is the percent of people who are still monthly active. I care a lot about using monthly active, not daily active, not weekly active. There are really good arguments for using daily active that people say all the time, because it's about intensity of usage, but what really, really matters is that you've got skin in the game and people come back to your product at least once a month, and then you build on top of that engagement, revenue, whatever your key metrics are, which we'll get to in a minute. But firstly I believe monthly active is the most important thing. The second thing is how do you really calculate it? So I've got a real graph for revenue in a few slides from Facebook from the first 250 days of our ads product which I'll show you. But I think what's really, really important is to say that it's very easy to calculate this curve even if you have a thousand or a hundred users. And what you do is you look at all of your users on the first day after they signed up. Were they monthly active? Yes, clearly. The first thirty days, everyone's going to be monthly active. I'd prefer if you defined a month as 28 days, by the way, 'cause multiples of seven is really, really good for these kind of metrics, because of weeks. But for the first 30 days, everyone has been active on their first day, on their second day, on their third day as a monthly active user whether they came back or not. And then what you do is you look at day 31. And obviously only some of your users will have been active for 31 days. Everyone who signed up a day ago was not active for 31 days. So what percentage of them were active on day 31? What percentage of them were active on day 32? And as you go further and further out in the number of days since registration, the number of people who have been on your service that long declines. Which means that your metric is gonna get noisier, right? As you get out to the far right hand side of that curve. But that doesn't mean you can't get a handle of where things flatten out very, very early on with very small numbers of users. The number one criticism people give me when I talk about retention as a metric, or even when you look at what Adam presented in his presentation earlier, is you need lots and lots of users, so you need millions of users to calculate that. That is not true. We did it with thousands of users in the early days of advertisers for Facebook for example. Or, you need to have had all of those users on for a very long time. Look at Adam's graph, it was multiple years with months, and months, and months. No, you can do it day by day and you can get to a really good place super, super fast using this methodology. So what are you looking for in a retention curve? You're looking for it to flat line above zero, not flat line like a heart monitor. You want it to flat line at some line that asymptotes with the x axis. There are three types of these curves that I've seen. The first type is where it asymptotes to the x axis, like really, really close, goes down to almost zero. You hear about a lot of those companies, you think about most games, right? Most games are smash hits. How many people use Farmville now versus how many people used Farmville at its absolute, please tell me you know what Farmville is. Yeah, okay. How many people use Farmville ... I'm feeling old, I had hair when I first did one of these. How many people use Farmville now versus how many people used to use it? Farmville was a smash hit. Then there's other ones which look like this, where they stabilize out. So you think about Minecraft, right? Or World of Warcraft. These games that somehow have network effects and people stay in them for a long time. The other version of this curve I've seen is the ones where they go down, they look like they're going to hit the x axis, and then the reaccelerate and they go up. There's a few different things that can cause that, right? So you have this overwhelming things that's driving you down to have zero retention, and somehow the numbers go up again. So things that drive that for businesses, and you actually I think mentioned this in your intro. One, network effects. So when all your friends get on Facebook you're much more likely to use Facebook. When all your friends get on Twitter, you're more likely to use Twitter, WhatsApp, Messenger, whichever social service you're talking about. There are network effects that really matter. Network effects also count in marketplaces. When you have enough sellers you can have enough buyers, when you have enough traffic you get more sellers. And so as marketplaces go, often what you find is these old cohorts that signed up resurrect and come back to the service. So network effects can drive the curve up after it's flat lined somewhere, or it's heading towards zero. The second thing that can do this is opening up a new interface. So think about it, a lot of people do release their apps first on iOS. I actually loved Jan's talk last week for this, by the point that he made of starting with Nokia, focusing on Android, because it's what most of the world use. But in Silicon Valley, we typically build our apps first on iOS more often than not. And then when someone releases a Android application, and this was something I saw with Instagram years and years ago. But when you release an Android application, people might have tried it on an iPhone and then traded their iPhone for an Android and no longer be able to access your service. People might have tried your service on a friend's iPhone, and then when you add Android they come back and use your service. So that's another thing that can drive you up again. And then the third thing that I've seen on these curves that drive these curves up again is adding categories. So think about Amazon. There was a really good discussion, was that Stuart earlier or ... Someone earlier in this talked about Amazon and how Amazon started with books. Because there was this large inventory available of books. But then they added in another category, and then they added in another category over time. And imagine, there'll be people who came to Amazon to buy that one book, but then two years down the line Amazon suddenly had kitchen ware, I don't know, or fashion. And they came back and then they bought fashion, and it brought them more use cases to come back more monthly active. This was true with eBay, we added motors. I remember being at eBay and we had to do all of our reporting on gross merchandise volume with and without motors, and no one had ever believed that you could buy a car online. And eBay did it, and it created a whole new use case for people, many of whom were existing eBay customers who now had one more category they could shop in. So, you want a curve that asymptotes to a line parallel to the x axis. You can do it with far more limited amounts of data than most people think you can by using the technique I outlined earlier, and I'll show you a real world example in a few slides. And there are three types of curves that I've seen. Curves that asymptote like this, awesome. Your company will survive. How big will it get, we have another question. Curves that go straight to zero, that's okay. You could be in games and your job is to produce one hit after another after another. EA doesn't expect whatever, FIFA 2012 to still have lots of users now if they've released FIFA 27. So it's okay, there are businesses where hitting the x axis is okay, but you need to know you're in one of those businesses. And then the third that I've seen is the curve going up, and that can be driven by network effects, that can be driven by being available on more platforms, that can be driven by adding categories to your marketplace or service. From this falls out a brilliant way to look at growth, and this guy Danny Ferante who was at Yahoo, who was one of the first members of the growth team, runs core data science at Facebook, he's totally awesome, he's really the brains behind the operation and the mouth. Danny brought this to Facebook, and it's a simple way to look at growth. We were looking at growth as how many users were we growing by, week on week. We looked at it at an absolute basis. And looking at it like that, we were like, "Huh, so we're signing up whatever it was, a million a week, and we're growing by whatever it was, 800,000 a week, I don't know." Cool, we're churning 200,000 people. And we never actually looked at it in this way. When we changed the way we look at things to say, "Okay, the net growth is 800,000 a week. The new users are one million a week. But what is churn and resurrection?" And what we actually found at the time, and I can't remember the absolute numbers so I'll just make some up, but imagine you're churning 1.3 million. So not 200,000, 1.3 million. You're resurrection 1.1 million. So, plus 1, minus 1.3, plus 1.1, you've got net 800,000 in growth, but that 800,000 growth is being driven, I think I did the math wrong, that 800,000 in growth is being driven by a different order of magnitude from churn and resurrection than you realized. And so that helps you focus on how do I stop people churning? How do I get people to come back to my service who are not coming back to my service right now? So once you have your retention curves, and once you start to have a business with retention, you need to look at your growth accounting, not just how many users are we acquiring. Make sense? Overall the number one thing I focus on for growth, the number one thing we focus on for growth, is retention. So what gets you retained in any given service? Magic moment. What is the moment when you used AirBnB? What is the moment when you used DoorDash, when you used Slack, when you used Facebook, LinkedIn, WhatsApp? Whatever the product, what is that moment when you went, "Yes. I've got it." On Facebook it's friends. You see that first friend, that person who, I've got completely the wrong crowd for this, the person from high school who you lost contact with as you grow older and moved away. But you see that person and you go, "Wow, I haven't talked to them in years. Let's see what's going on with their life." And they've had a kid and lost their hair too and you feel better about yourself. That is a magic moment on Facebook. So how quickly can you drive the user to that magic moment? You look at AirBnB, right? They've talked about the delight that they give people, they've talked about these different screenshots of the stages in the user journey that they created as a map of the user journey. Getting that first person reserve your house, staying at that first property, those are our magic moments. Ebay, buying your first item from a stranger online. Nowadays that doesn't seem crazy, but back when I was working at eBay in the UK that was revolutionary, and people really, their eyes lit up when you talked to them about it. In Germany they had this great ad , that showed the buildup of when you were waiting for the auction to finish and for you to make your first purchase, and that was such an adrenaline, endorphin moment, you know? That users loved it. Nowadays it seems a long time ago, but those magic moments are what you need to look for. So how do you find them? Well you can find them in the non scalable way, which is, don't they all just sound relatively obvious? You can actually think through, what is the experience of my product, what do I want people to do? You can ask your first users, "Okay, you're keeping using my product, why are you keeping using my product? What is the thing that makes you happy to use my product? What was that first moment?" It's non scalable, but it generally works. When you ask people, "What is the best thing about Facebook?" The first thing they say is, "Connecting with friends and family." In all the research, "Why do you like Facebook?" "Oh, it helps me connect with my friends and family." So you can do it the non scalable way, or you can do it the scalable way. Look for correlations. So, Mark has talked at startup school himself, and he said that we focused on getting people to ten friends in fourteen days. It's a smooth curve, right? Number of friends on the x axis and percent retained on the y axis, it's not like there's a step change at ten, if you get nine you don't retain, if you get eleven you do and it all changes at ten. No, it's clearly a smooth curve, and we picked a point on the curve. But there's a strong correlation between number of friends you've got in the first fourteen days and whether you retain or not. There's a correlation between number of friends in the first day, but there are correlations for any product. Like, did you place a bid, did you do a buy it now, or did you list an item on eBay, was the thing that was completely correlated with retention for eBay. You can imagine, I don't know, I mean DoorDash was founded at Stanford, right? You can imagine for DoorDash, you've placed your first order and that order arrives to your door. And whether you've placed an order or not, how many orders you've place in the first week, probably they are actually correlated with whether you retain or not. So you need to look for the magic moment of your product by talking to users, and then try and validate that with correlations. Make sense? Cool. I love this, I put it in every presentation I do. So I'm lucky enough to have been trained in physics at Cambridge in the UK, and my favorite lecture series, not my favorite lecture but my favorite lecture series was this thing called Dimensional Reasoning. And so it's easy for me to stand up here and say, "Okay, so you need to get flat retention and then you have a viable business." That's not actually true. That's kind of a glib statement. If you're producing a social network and you have five percent retention, you're not going to long term have a tremendous social network. Or a messaging app, like what use would WhatsApp be if only five percent of your friends actually used WhatsApp who signed up for it? I mean, it'd be useless, right? And there's no way we could release one billion plus numbers of active users if that was true for us. On the other hand, if you have a marketplace that is selling fashion, and you have five or ten percent retention, but those people spend a lot of money, you have a totally viable business. And so the level of retention you need varies dramatically by the category that you're in. Obviously the higher the better is true in everything, like if you can have an eBay type business model or Amazon type business model with 90 percent retention, you'll probably end up bigger than Amazon. I mean that would be incredible. But I think the point I like to make is, you can figure it out. And I love this as an inspirational story. So Jeffery Taylor was this British physicist, he was on the edge of the Manhattan Project, he wasn't in the core. He ended up winning a Nobel Prize later for a different area. And the US and Russian governments were releasing these images to say, "This is how big our bomb is." But they weren't releasing the actual power because that was a closely guarded state secret. And so what Jeffery Taylor did is he used this technique called Dimensional Reasoning, where you look at the units in the metric you are trying to achieve, so joules, right, for energy is joules. Kilograms time meters squared over seconds squared, that's what joules decomposes into. In this case, kilograms, what are you gonna use, the volume of the air times the density of the air which handily is one, so you've now got meters cubed times meters squared over seconds squared. So meters to the five time seconds to the minus two. The time is on here, the diameter, the radius of the sphere is on here, you just plug the radius in as m, you plug the diameter, the time in as seconds, and you end up getting 25 kilotons as the output. You get ten to the fourteen I think is what it works out as. And it turns out the bomb was 21 kilotons as measured by the US governments. He could take one photo and figure out the most closely guarded state secret in the United States at the time. You can figure out what retention you need to have if he can figure that out. So here's what I promised, this is an actual curve, I presented it at a few different events, so I've taken off the numbers because I'm worried they're state secrets. But this is from 2007 to 2008. I joined the week we started our ads business at Facebook, our self service ad business by us. The pink speaks to the right hand axis, the blue speaks to the left hand axis. What the blue is, is how many advertisers had been in the system at least one day, two days, three days, 'cause the x axis is number of days from acquisition, makes sense. So the thing has only been around 250 days, so zero people had been advertisers 251 days ago, and on the 251st day. And all the people have been active at least on day zero. And then each of those pink data points is the total revenue we achieved from every user who had registered, who was using the service on their 30th day or their 31st day, divided by how many users signed up 30 or 31 days ago. Not how many were active, right? How many signed up. So total in the cohort who signed up 30 or 31 days ago, you divide the revenue they made in a day by that number of users. And that gives you that shape of curve that's up there. I stumbled over my words a bit there, I promise you if you look over it again it makes perfect sense. I did this with Danny back in 2008. The really important things I want you to take away from looking a this curve is, look how flat the pink line is. Like, I've been talking about curves flattening out, this is an actual curve flattening out. And look how quickly it's flattened out. By 100 days you can pretty much predict what the line is going to be at 200 days. I've seen this over and over, I've seen this with engagement. So if you divide time spent by the same denominator, I've seen it actually for sentiment. If you have a large enough sentiment survey, and you divide it by that same denominator. I have seen it for posts. I have seen it for photos uploaded, messages sent. Like, you can use the same techniques that I talked about for determining how much retention you have and whether your business is viable, to figure out what is the lifetime value of one of your customers. If I was to acquire the total addressable market for my business today, what revenue will I be making in 100 days? That's what this curve allows you to do. You can predict it for posts, you can predict it for page views, time spent, whatever you want. The same methodology works for engagement that works for retention. And actually retention's often the biggest driver of this metric. And we did this with very little data. Later on there's a stat we are okay releasing, which was end of 2012 we had about 700,000 active advertisers, beginning we had about 300,000 active advertisers. And this was five years before that, so you can imagine there was a very small data set that was behind calculating this. The last thing is I said things get noisy the further out you go. You can literally see that on this graph, right? The variance in the numbers the further to the right you go on the graph. So, how do you operate for growth? Most important thing is have a clear goal. Brian who is awesome on growth marketing at Facebook, great guy, he always says, "If you are a growth team your product is a number." One of the most brilliant things I think that Mark did in the early days of Facebook was he aligned the entire company on monthly active people as our goal. Now, why does this matter? It matters for a bunch of reasons. But the biggest and most important reason is I think it aligns everyone. When you have a company of more than two or three people, by the way a team of more than two or three people, certainly when it gets to the hundreds, you can't control what anyone does. I mean, you could probably control what one person does, but the the other 199 would do whatever the hell they wanted. So what do you want to do? You want to make sure everyone is pointing in the same direction and that direction is generally right. So let's think back to the early days of any company, whether it's Facebook, whether it's AirBnB, LinkedIn, whatever you want to look at. It would be completely reasonable to have a goal of revenue. Right? You want your company to stay alive, you want to make enough money that your company can still be viable, that you can employ people, that the valuation goes up. So a reasonable person could say, "I think we need to drive towards revenue." At the same time you could have a metric that was like, if you're AirBnB, number of hosts. If you're Facebook, number of posts. If you're DoorDash, number of orders. I don't even know what these companies' metrics are beyond Facebook's. You could easily have a production metric or a consumption metric. You could have a time spent in our service metric, and every one of those metrics would be a completely reasonable metric to drive for. So, you've got ten reasonable people. Maybe five of them think you should have monthly active people, one is really focused on revenue, two are looking at time spent, and two are looking at number of posts. Everyone is pushing in a reasonable direction that is vaguely aligned, but probably will have conflict. If you want to get an ad in front of the user as quickly as you possibly can, you may not necessarily want to put friends in front of them immediately. If you want to get them to post, you may dial up the composer and push down the ads. I don't know, like these are trade offs that you could be making. And if the whole team isn't clear what the goal is they will be in conflict. So having a clear goal that your company looks to drive is really, really important, and having that goal be MAP in my opinion, is the best metric that you can drive for because it is that once a month visitation that base level you can drive off of if you're going to do a consumer internet company. But fundamentally the most important thing is pick a goal, your team's product is a goal, focus on that goal, align the company around that goal, and then you are going to operate correctly for growth. The other thing I always say is you don't need a growth team. If you are like a small company, if you're ever series A, don't have a growth team, don't hire someone like me. The whole company's job should be growth. I'm very happy at Facebook so I don't want a job offer. But the whole company's goal should be growing. The purpose of a startup is to grow. So what is the number you want to grow, and align the whole company against growing that number. Does that make sense? Now Naomi is amazing, and Naomi is someone people don't know that well out of Facebook, but she runs product and engineering for growth, she's brilliant. She's been at Facebook 12 years, so when you say I'm original, I feel not original every day. She sits like three feet from me, she's amazing. Anyway, she has this approach that we use to iterate through our growth planning cycles, which I think works really well. Understand. First of all, understand what is going on. Look at all the data you have. Talk to your users. Ask your users what they think. Understand everything that is going on with your product that you can possibly understand. What matters. Retention, are you churning too many? Do you have the retention? Is the revenue going up on a per user basis? What are the acquisition channels you're getting? Which channel are people resurrecting through? Are they resurrecting through email, is it push notifications? What's going on? Understand what's going on for your system. Have that all tracked. Then identify. Look through all of those different metrics, all of that different research you get in, and identify where are there opportunities? Where do you see big drop offs? Do you have a lot of people with push notifications turned on who are not receiving push notifications? Do you have a lot of people who are receiving push notifications who are not clicking on them? Do you have loads and loads of social media traffic but no search traffic? Are your email viral invites working but your SMS ones not, and what is the difference between those? Like, go and look at the data and identify where there are gaps, and then try and double down on fixing those gaps in the execution phase. To this day this is still how we run growth at Facebook, and you can even see a talk Naomi and I gave online nine years ago I think at F8. Eight or nine years ago, where we talk about using this approach, and we have kept it going. Understand what's going on, identify the opportunities in that data you've understood, and then execute against it. Another really interesting thing I think was said earlier is, "Data debt sucks, and you're going to regret it later, and your intuition is going to run out at some point and then you're going to want data." In 2009 we actually shut down all of the work the growth team was doing during January and we only spent the time logging and making sure every critical flow on Facebook, registration. At the time we didn't log clicks on emails, so clicks on emails. It seems obvious now, but we didn't have it. Clicks on emails, did you then actually log in or did you fail in the password entry, all of these different things, we logged them so that we could understand what was going on, and that was super critical 'cause actually in March that year we had a massive growth slow down, and we would not have been able to understand what was going on if we hadn't done the logging in January, because what went on was actually super nuanced. And without that logging in place we wouldn't have known. Get the data logged so you can run through this process. So speaking of data, I am very, very lucky at Facebook, I do growth marketing, I run the awesome internationalization team, but I also run the analytics team for the company and that's been a couple of years now. When I talk about data to people internally, I like to say these three points. There's a meme in the valley that you operate either for user experience or for metrics. And I really want to bust that meme. Data, when used in the right way, gives you empathy about your users. One example I've talked about in the past is we launched a product on Facebook that you may have seen on your big 42 inch monitors, no one has a big 42 inch monitor in here, but I'm sure some of you do at home. That has on the right hand side, all the friends you could chat with on Facebook, but at the top it has this list of stories that's ticking over called ticker. When you're on a small screen, that product used to collapse into the right hand column of Facebook and push things down so that you could still see these stories ticking over. That product worked great for all of us who had huge numbers of friends, who were creating a lot of content on big screens. But if you went to the Philippines, where there was huge usage in internet cafes, and people were on narrow screens, the majority of them ended up having that in the right hand column, and the majority of them were low engagement with Facebook and couldn't see people you may know below the fold. And so we actually lost friending amongst users who are very different to ourselves, and we only figured out what was happening by looking at the data and saying, "Ah, why is this bad on small screens and awesome on big screens? Why is it not so great for low engagement users and super for power users?" It was because we used the data to understand these users who were different to us here in Silicon Valley. Now think about it. I presume I don't know, I'm seeing a lot of Apple product here, I have a Lenovo at the moment. But most of you probably have iPhones, who has iPhone? Yeah, who has an Android? You're the best. Most people in the world are using Androids, but everyone in this room has an iPhone first and foremost. You don't understand, I'm actually forgetting how to use iOS now which is actually a problem in my job, but like I've got an Android because the majority, like huge but maybe not the majority, a vast number of our users use Android as Jan said in the last talk. And I need to understand what the Android operating system offers. But I can't understand Android and iOS well as a user at the same time, so I use data to understand what's going on in iOS. You need to gain empathy for your users, and research is great, and talking to your customers is great, but if you listen to the Twitch guy Emmett, you know, talk to your users then go away and build the product to serve them, and look at the data in my opinion would be the next thing to validate if you built the right thing, if you're doing the right thing, if your research actually ties up with what your users have said to you. So data gives you empathy. It's not a trade off between metrics and users. If you build the right metrics, you are optimizing for the users. Two, data predicts the future. Over, and over, and over again, and I love this quote, it's a science fiction author I think. But the future is here, it's just not evenly distributed yet. So, those curves that show you the retention, when I said that you can look at your total addressable market and say, "If I signed them all up, how much revenue would I get?" Or you just look at who you signed up today. Let's say, back in those days we signed up a thousand advertisers in a day. I could tell you how much revenue they would contribute a hundred days out because of that curve. Now also, your users are going to ramp up. So a user who shops in one category, if you're eBay or if you're Amazon, and then buys in a second category and a third category is clearly going to get more valuable, right? Shopping the shop, driving up same store sales is a common technique in e-commerce. Okay, so how many of your users are increasing the number of categories they're shopping in? You see someone sign up, what likelihood is it that they're going to add extra categories and what is your revenue going to be in the future? You can predict that with data. AB tests are another great thing. See all these people walking in the science march, believing in science, and then when you apply the scientific method to their product and run an AB test, they shipped the thing that looked bad in the AB test, because they didn't believe the AB test. I see that with all kinds of startups I talk to. AB tests tell you what's going to happen when you ship then 100 percent, use them. It works. Finally, and this is something Mark actually added recently for me, data helps you make decisions faster. If you can not, as the CEO, have to have a conversation over something where you could just run a test, your company is going to be able to move quicker because every decision won't have to come to you. People in the team, if they're clear on what that goal is, that north star metric is, they can run the test, see if they move the metric, and then only come to you when they know if they've moved the metric or not. Decisions like that help you move faster. Knowing what your most important priority is because you've looked at the data, that helps you move faster. Make sense? Cool. So now, you wanted to push for as much of the time as possible so I'm pushing for as much of the time as possible. So let's talk about tactics. First hundred users, Sam talked about this brilliantly, if you actually listen I thought Tracy in the previous conversations was just fantastic when she talked about getting 30 of her friends in the construction industry to try PlanGrid, and then asking them to pay and 29 of them kept paying. Like, ask your friends first for your first hundred customers. Get people who will give you honest feedback, and I love the point about "Make them pay," if it's a paid product. Second, research and then reach out to who you'd like to have as your users. Totally makes sense, right? Like, you can do what ... Like, how Oscar was discovered by the Lob team. Right, just email a bunch of people. Don't get caught in a spam filter, don't just auto-generate emails or you'll definitely get caught in a spam filter. But send personal emails, get introductions. See which friend you have in common. I emphasize by the way, I have never started a startup so I might be entirely the wrong person to tell you about first hundred users, but Sam asked me to. Third, social media and PR. PR is non scalable but it can give you amazing bumps, amazing bumps. And in the early days I think PR is a good growth lever. Later on I think companies don't realize when PR has stopped being a growth lever, because they have these other metrics. But you can get this great one time bumps, just know you can't get them every single week, and sometimes they are a one off. And finally, buy ads. I am not as against ads as Sam is as a great method for growing users. I think that buying ads can be absolutely fantastic, I built my career buying ads. Two of the most valuable internet companies, Google and Facebook make all their revenue form selling ads. Like, ads are actually a good thing if you get the targeting right, and we're going to talk about that in a second. If you are having a growth team, if you are focused on growth tactics, what people talk about as growth hacking, although I hate the term. If you pour fuel, this is a picture from a guy called Nick on my team, Nick , and I did not tell him to hold his hand into the flames, which we'll see in a second. If you pour lighter fluid onto dead coals with no spark, you get nothing. And if there's one thing to take away from this lecture, you need to have product market fit to drive growth, you need retention to drive growth, otherwise every growth tactic, every acquisition tactic you could possibly run doesn't matter. But, if you have a spark there, if you have a glowing ember, you can pour fuel on the fire of your product and that is what growth tactics does. So I look at things based on channel, targeting, creative, and then conversion. So, if you think about channel, and I'm gonna try and do some greatest hits here. If you look at SEO, you have to think about key word research. Which key words do I want to show up for on Google, on the other search engines, and make sure you have content targeted against those key words. You need to get links. Links from the internet to your site. If you're a Silicon Valley startup, you typically get a lot of links off high value sites, so you usually don't have to worry about that. But then you need to link inside your site. Many people will tell you links don't matter anymore. In my experience that is not true. I don't know why it's not true, but it's not been true in my experience. And then finally there are a bunch of basics out there on SEO. You can read them up, there are a load of websites that talk about them. SEO , Moz, there are a bunch of websites out there you can read about SEO tactics. Four page search. The biggest thing I see people do wrong is they don't think about incrementality. They buy page search and they don't necessarily say, "Well, would I have got those customers anyway?" So look really hard. If you're gonna buy page search as a startup, you should see a step change in your line. Your marketing team, if you've reached the stage where you have a marketing guy, should show you a line that is flat, and then they turn on page search and it goes up. That's the same of any ads if you are a small startup. If you're a big company, incrementality analysis, there's this great Berkeley professor Steve Tadelis, who's done a deep study in it with eBay that I love and I think he's really smart. Big companies, you have to be really very smart about getting incrementality. You're a small startup, show me a step change. Do think about marginal, not absolute ROI. So people always look at like, I spent this much, I got this much, how valuable is it? That's not what you want to look at. You want to look at the last dollar you spent, how much did you get for that? Not overall. Does that make sense? Not as many nods. I'm going to blast through that. Look at marginal ROI, look at diminishing return curves in economics. And then again, you need to do your keyword research. So email, SMS, and push notifications. This is something every startup gets wrong. We always optimize for ourselves. Have you seen the Instagram thing on the internet where someone shows a phone of an Instagram user with a million followers when they've posted something and they have push notifications on, and the stream just goes, and all the notifications are coming in at high speed? That's what we're like for our own products. For our products, we're the power users. And by nature every startup I have gone and talked to has always optimized for themselves. Has always optimized for the power user. And so by optimizing for the power user, what do you do? Well, we shouldn't put that on by default because it's gonna completely spam my home screen. Power users are smart, if you give them the options they know how to turn off notifications. It's your marginal user you should care about with notifications. That person for whom they receive their first like on Instagram, or the host that gets their first notification on AirBnB, or the seller that gets their first bid on eBay, or the buyer who's just been outbid in their first item. That's the person that needs to get the notification and needs to respond to it, and you need to optimize for in your head. Marginal user is the number one thing. In email, SMS, push notifications, and even offline mail which Lob was talking about and is still very important for many companies. People get wrong, they optimize for themselves. Beyond that, you need to make sure it gets delivered, opened, clicked on, et cetera. There's so much more here and if I push through all of it I'm not going to get to any questions. Do you want me to get to questions?

*[0:39:27]*
**Speaker 1**: This is pretty important. How about this and no questions.

*[0:39:30]*
**Alex Schultz**: This and no questions. Is everyone okay with that? Is that okay. Okay, sweet. So think about on-site merchandising. You own a lot of media. There's this idea of earned, bought, and owned media, does that make sense? So if you post on Facebook and it gets loads of likes you've earned that media, right? If you bought an ad on Facebook or Google or somewhere, you've bought that media. And then if they're on your own website, you own that media. People underestimate the value of owned media. If you have a lot of traffic on your site, ask them to invite their friends. Ask them to complete that next action in your product. Think about your on-site merchandising as an extension of your product, but understand the on-site merchandising is a marketing problem. So, how can you trigger the right thing? Someone has just bought and item for you, immediately like Amazon does, trigger for the a set of suggestions of like, why don't you buy this, this, and this? Or customers who bought this also bought this. Works incredibly well. What about creative? Creative is really important. It should be personalized, because they're on your site, you know their name. "Hey Alex, why don't you buy these items that other similar customers have bought?" Performs way better than "Buy these other items that similar customers have bought." That's one similar personalization. But instead of just saying, "This customer bought this item," you should do a prediction. "This person, all the items they've ever bought, let me predict for them in a personalized way what item they should buy." Look at Facebook and how often we say "Five of your friends have done x." Social context, that's personalization. Use people's own profile picture. We have a great personalization example where when you are on a Facebook page, we show you what your ad would look like if you bought it with a "Buy this" button underneath it. It could not be more personalized. On-site merchandising gives you so many opportunities to deeply, deeply personalize the creative. And finally, paid and organic social. I do work for Facebook, we do make all of our money from ads, and I personally think that I am not the best person to speak about buying ads on Facebook, because I'm the guy that buys ads off Facebook to drive traffic to Facebook. But what I'll say is, if I left Facebook today and I was at a startup, I would be crazy not to use our custom audiences and look-alike targeting. So I highly, highly encourage you if you think about nothing else on Facebook ads, to look into those. It's a great way for you to say, "These are my best customers," upload the list to Facebook, "How do I find more customers like those?" It works incredibly well for a lot of companies. And the other thing across all social media is to a great extent you're dealing with interesting squared, because the click through rate on your thing, the like rate or whatever on your thing, will be related to how interesting it is, and also the distribution, whether it's being shared via a messaging app, reshared on Twitter, or on Facebook, will be driven by whether people found it interesting. Make sense? Cool. Okay. Targeting. Behavioral is most important, so what you did is much more important than who you are. There are 50 year old, 60 year old parents and grandparents who need two friends to be active on Facebook, and there are some who need 500, and there are teenagers who need 500 friends, and some who need two. And you're much better to figure out, like, what is the behavior that tells you that, than say on a demographic basis I'm going to do this versus this. You know? People who buy Nikes, men's Nike trainers, typically are men. But sometimes buy for their boyfriend, or their husband, or their dad, or whatever. And so if you just target women with women's trainers and men with men's trainers, you are making a mistake. What you should do is look at what signals you to show the right ad, or the right content to the right person at the right time. Targeting matters. I've talked about this before. Okay, creative. Creative is important, but it's nowhere near as important as the above two unless you have a ten x creative. If you produce the Dollar Shave Club video, probably it's very, very worth it, but most people don't produce a piece of creative that amazing that changes the world. Typically I find targeting comes before creative in every single way, and channel comes before that. Does that make sense? So creative is awesome and really important and can give you a big uplift, but think about your channel first, and your targeting second. If you have creative though, you should put it in context, you should personalize it, you should give it a call to action. On call to action, this is a great example. Where, on every ad back in the day on Facebook, we had a little link at the top that said "Advertise." We translated it wrong by mistake in French, we found this with the data, and in French we translated it to "Crée un ad", create an ad. And we had significantly more, actually 40 percent more acquisition from that channel that was at the time our single most important channel for advertiser acquisition. We took the French translation and we reverse translated it and changed the whole world. Instead of saying the kind of passive "Advertise" to the active "Create an ad" and we achieved a 40 percent bump in that most important channel for us, for advertiser acquisition. That is the call to action. Like what you say matters. So when I say creative doesn't matter as much as channel and targeting, it still matters a lot. And the words you use matter a lot. This is the example I said about in context with intent, if you are on your own page, this used to be the ad we would show to you that say, "Hey! This is what your ad would look like elsewhere on the site." And you can see that by hour, this was the hour that we doubled advertiser acquisition for Facebook, with the push of the weekly push. If you want to see more about this, Brian Hale does a great talk about this at F8, and it's online. And then the last thing, yes. The last thing is conversion. Conversion is really, really important but it's not important until you've thought about all the other things first. So things to think about with conversion, the landing page people land on will determine what your conversion rate is going to be. If you can, don't have one button on it that they then have to click to go to the registration form. Put the registration form in the landing page. It's important to know what your most important metric is though. Back at eBay, what we had was this original metric, confirmed registered users, and a new metric, activated confirmed registered users. And an activated confirmed registered user was someone who bought an item, bid on an item, listed an item. And a confirmed registered user was someone who registered and confirmed and that was it. To get confirmed registered users, we wanted to pump all of our search traffic, this was back in 2004, we were spending huge amounts on search. We wanted to pump all of our search traffic at the registration page. To get activated confirmed registered users, we actually wanted to land it on the search term that you were looking for. So if you were looking for a trampoline, show you the trampolines you could buy on eBay. That change, and we changed all of our advertising to change the landing page, dropped confirmed registered users significantly that we were acquiring for eBay. But it increased activated confirmed registered users. It increased revenue and lifetime value per registered user, and it increased total revenue per day from the people we were acquiring. So although I think you can focus on the registration form really hard, in e-commerce sometimes it's really, really important to land people on what they're searching for so that they will activate and they will buy on your service. So it's not as simple as just put the registration form online. But we did do that at Facebook. So the final thing I'd like to say is Facebook moves fast. And move fast is an absolute core value for us as a company. Like core, core, core value, and we still do bold things like taking messenger out of Facebook, that was challenging at the time but I think people have now seen how valuable a decision that was. And that was a project that I was deeply involved with and ran. We're willing to move fast, we're willing to be bold. Because fundamentally, a good plan violently executed on today is better than a perfect plan next week. Data helps you move fast, these growth tactics help you move fast. Talks about making decisions with 70 percent of the data, not 90 percent of the data, and I deeply believe in that. So move fast, focus on retention, and have one key metric that you care about for your company's growth. Thank you. Okay, apparently we have time for one question. I'm sorry. Go for it.

*[0:48:07]*
**Speaker 3**: So you mentioned that for the magic moment we can kind of tell when they happen, but how do you think about creating those

*[0:48:11]*
**Alex Schultz**: So the question was, for the magic moments you can kind of tell how you can figure out when they happen, but how can you create them? I mean, honestly if you look at LinkedIn, Facebook, Twitter, every social network in the known universe, what we do is we immediately as you to import your contacts. So, once we know that friends is the thing that we need to do for you, we focus on what is the way to remove friction from getting you friends. If you look at the example I used with eBay, we hadn't really figured out that activating you was important to generating the revenue long term, so we shifted the landing page from the registration form to the search results, so we got you to the item you wanted to buy as quickly as possible, and that caused you to activate and get to that magic moment faster. So, those are the kind of things. Once you identify it, I actually think the tactics fall out very logically. My boss Havi likes to say that common sense is the least common of all sense, and I agree with him. But if you step back and you just look at, "Okay, if I want more people to buy an item on eBay, should I land them on the registration page or the search results page?" You'd probably land them on the search results page, and that would get them to that magic moment faster. Thank you, okay.
*[0:49:16]*